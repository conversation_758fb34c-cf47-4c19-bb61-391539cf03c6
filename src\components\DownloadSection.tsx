import React from 'react';
import { Download, Copy, Github } from 'lucide-react';
import { useProjectContext } from '../contexts/ProjectContext';
import { generateZip } from '../utils/zipGenerator';

const DownloadSection = () => {
  const { project } = useProjectContext();
  const [isDownloading, setIsDownloading] = React.useState(false);
  
  const handleDownload = async () => {
    if (!project.structure) return;
    
    setIsDownloading(true);
    try {
      await generateZip(project);
      setIsDownloading(false);
    } catch (error) {
      console.error('Error generating zip:', error);
      setIsDownloading(false);
      alert('Failed to generate ZIP file. Please try again.');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500">
      <h2 className="text-xl font-semibold mb-4 text-gray-800">Your Project is Ready!</h2>
      
      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-md">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-green-100 rounded-md p-2">
              <Github className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-900">{project.name}</h3>
              <p className="text-sm text-gray-500">
                {project.frontend && `Frontend: ${project.frontend}`}
                {project.backend && `, Backend: ${project.backend}`}
                {project.database && `, DB: ${project.database}`}
              </p>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className="flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:bg-blue-300"
          >
            {isDownloading ? (
              <>
                <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                Generating ZIP...
              </>
            ) : (
              <>
                <Download className="mr-2" size={18} />
                Download ZIP
              </>
            )}
          </button>
          
          <button
            className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <Copy className="mr-2" size={18} />
            Copy to Clipboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default DownloadSection;