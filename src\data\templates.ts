import { ProjectTemplate } from '../store/useAppStore'
import { FileNode } from '../types'

// Helper function to create file nodes
const createFile = (name: string, content?: string): FileNode => ({
  name,
  type: 'file',
  content: content || `// ${name}\n// Generated by ProjectForge\n`,
})

const createFolder = (name: string, children: FileNode[] = []): FileNode => ({
  name,
  type: 'directory',
  children,
})

// Getting Started template - Perfect for beginners
const gettingStartedTemplate: FileNode = createFolder('getting-started-project', [
  createFile('.gitignore', `# Dependencies
node_modules/
.pnp
.pnp.js

# Production
/build
/dist

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db`),
  createFile('package.json', `{
  "name": "getting-started-project",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "lucide-react": "^0.344.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@vitejs/plugin-react": "^4.2.1",
    "autoprefixer": "^10.4.16",
    "eslint": "^8.55.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5",
    "postcss": "^8.4.32",
    "tailwindcss": "^3.3.6",
    "typescript": "^5.2.2",
    "vite": "^5.0.8",
    "vitest": "^1.1.0"
  }
}`),
  createFile('vite.config.ts', `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  }
})`),
  createFile('tsconfig.json', `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`),
  createFile('tsconfig.node.json', `{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}`),
  createFile('tailwind.config.js', `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}`),
  createFile('postcss.config.js', `export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}`),
  createFile('index.html', `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Getting Started Project</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`),
  createFile('README.md', `# Getting Started Project 🚀

Welcome to your new React + TypeScript + Tailwind CSS project! This template provides a solid foundation for building modern web applications.

## 🛠️ What's Included

- **React 18** - Latest version with modern features
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool and development server
- **ESLint** - Code linting for consistent code quality
- **Vitest** - Fast unit testing framework

## 🚀 Getting Started

1. **Install dependencies:**
   \`\`\`bash
   npm install
   \`\`\`

2. **Start development server:**
   \`\`\`bash
   npm run dev
   \`\`\`

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

\`\`\`
src/
├── components/     # Reusable UI components
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
├── App.tsx        # Main application component
├── main.tsx       # Application entry point
└── index.css      # Global styles
\`\`\`

## 🧪 Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run preview\` - Preview production build
- \`npm run lint\` - Run ESLint
- \`npm run test\` - Run tests

## 🎨 Styling

This project uses Tailwind CSS for styling. You can:

- Use utility classes directly in your JSX
- Customize the theme in \`tailwind.config.js\`
- Add custom CSS in \`src/index.css\`

## 📚 Learn More

- [React Documentation](https://reactjs.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Vite Documentation](https://vitejs.dev/)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Happy coding!** 🎉`),
  createFolder('public', [
    createFile('vite.svg', `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--logos" width="31.88" height="32" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 257"><defs><linearGradient id="IconifyId1813088fe1fbc01fb466" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%"><stop offset="0%" stop-color="#41D1FF"></stop><stop offset="100%" stop-color="#BD34FE"></stop></linearGradient><linearGradient id="IconifyId1813088fe1fbc01fb467" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%"><stop offset="0%" stop-color="#FFEA83"></stop><stop offset="8.333%" stop-color="#FFDD35"></stop><stop offset="100%" stop-color="#FFA800"></stop></linearGradient></defs><path fill="url(#IconifyId1813088fe1fbc01fb466)" d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z"></path><path fill="url(#IconifyId1813088fe1fbc01fb467)" d="M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z"></path></svg>`),
  ]),
  createFolder('src', [
    createFile('main.tsx', `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`),
    createFile('App.tsx', `import React, { useState } from 'react'
import { Header } from './components/Header'
import { Hero } from './components/Hero'
import { FeatureCard } from './components/FeatureCard'
import { Button } from './components/Button'
import { Code, Palette, Zap, Shield } from 'lucide-react'

function App() {
  const [count, setCount] = useState(0)

  const features = [
    {
      icon: <Code className="h-8 w-8" />,
      title: 'TypeScript Ready',
      description: 'Built with TypeScript for better development experience and type safety.'
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: 'Tailwind CSS',
      description: 'Utility-first CSS framework for rapid UI development.'
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: 'Lightning Fast',
      description: 'Powered by Vite for instant hot module replacement and fast builds.'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: 'Best Practices',
      description: 'Configured with ESLint, Prettier, and modern development tools.'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <Hero />

        {/* Interactive Counter Section */}
        <section className="my-16 text-center">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Interactive Counter
            </h2>
            <div className="text-4xl font-bold text-primary-600 mb-6">
              {count}
            </div>
            <div className="flex gap-4 justify-center">
              <Button
                onClick={() => setCount(count - 1)}
                variant="outline"
              >
                Decrease
              </Button>
              <Button
                onClick={() => setCount(count + 1)}
                variant="primary"
              >
                Increase
              </Button>
            </div>
            <Button
              onClick={() => setCount(0)}
              variant="secondary"
              className="mt-4"
            >
              Reset
            </Button>
          </div>
        </section>

        {/* Features Grid */}
        <section className="my-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-12">
            What's Included
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
              />
            ))}
          </div>
        </section>

        {/* Getting Started Section */}
        <section className="my-16 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Ready to Build Something Amazing?
          </h2>
          <div className="max-w-2xl mx-auto text-center">
            <p className="text-gray-600 mb-6">
              This template gives you everything you need to start building modern React applications.
              Explore the code, customize the components, and make it your own!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Start Building
              </Button>
              <Button variant="outline" size="lg">
                View Documentation
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

export default App`),
    createFile('index.css', `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}`),
    createFolder('components', [
      createFile('Header.tsx', `import React from 'react'
import { Github, ExternalLink } from 'lucide-react'

export const Header: React.FC = () => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">GS</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-800">Getting Started</h1>
              <p className="text-sm text-gray-600">React + TypeScript + Tailwind</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <a
              href="https://github.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <Github className="h-5 w-5" />
            </a>
            <a
              href="https://vitejs.dev"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ExternalLink className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </header>
  )
}`),
      createFile('Hero.tsx', `import React from 'react'
import { Button } from './Button'
import { ArrowRight, Sparkles } from 'lucide-react'

export const Hero: React.FC = () => {
  return (
    <section className="text-center py-16">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-6">
          <Sparkles className="h-8 w-8 text-primary-500 mr-2" />
          <span className="text-primary-600 font-semibold">Welcome to your new project!</span>
        </div>

        <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6">
          Build Amazing
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
            {' '}React Apps
          </span>
        </h1>

        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          This template includes everything you need to start building modern web applications
          with React, TypeScript, and Tailwind CSS. Fast, type-safe, and beautiful.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="primary" size="lg" className="group">
            Get Started
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button variant="outline" size="lg">
            View Components
          </Button>
        </div>

        <div className="mt-12 text-sm text-gray-500">
          <p>✨ Generated by ProjectForge</p>
        </div>
      </div>
    </section>
  )
}`),
      createFile('Button.tsx', `import React from 'react'
import { cn } from '../utils/cn'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className,
  children,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'

  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500',
    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500'
  }

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}`),
      createFile('FeatureCard.tsx', `import React from 'react'

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
}

export const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="text-primary-600 mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-800 mb-2">
        {title}
      </h3>
      <p className="text-gray-600">
        {description}
      </p>
    </div>
  )
}`),
    ]),
    createFolder('hooks', [
      createFile('useLocalStorage.ts', `import { useState, useEffect } from 'react'

/**
 * Custom hook for managing localStorage with React state
 * @param key - The localStorage key
 * @param initialValue - Initial value if key doesn't exist
 * @returns [value, setValue] tuple
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(\`Error reading localStorage key "\${key}":\`, error)
      return initialValue
    }
  })

  // Update localStorage when state changes
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.warn(\`Error setting localStorage key "\${key}":\`, error)
    }
  }

  return [storedValue, setValue] as const
}`),
      createFile('useCounter.ts', `import { useState, useCallback } from 'react'

interface UseCounterOptions {
  min?: number
  max?: number
  step?: number
}

/**
 * Custom hook for managing counter state with optional constraints
 * @param initialValue - Starting value for the counter
 * @param options - Optional constraints (min, max, step)
 * @returns Counter state and control functions
 */
export function useCounter(initialValue = 0, options: UseCounterOptions = {}) {
  const { min, max, step = 1 } = options
  const [count, setCount] = useState(initialValue)

  const increment = useCallback(() => {
    setCount(prev => {
      const newValue = prev + step
      return max !== undefined ? Math.min(newValue, max) : newValue
    })
  }, [step, max])

  const decrement = useCallback(() => {
    setCount(prev => {
      const newValue = prev - step
      return min !== undefined ? Math.max(newValue, min) : newValue
    })
  }, [step, min])

  const reset = useCallback(() => {
    setCount(initialValue)
  }, [initialValue])

  const set = useCallback((value: number) => {
    setCount(value)
  }, [])

  return {
    count,
    increment,
    decrement,
    reset,
    set
  }
}`),
    ]),
    createFolder('utils', [
      createFile('cn.ts', `import { type ClassValue, clsx } from 'clsx'

/**
 * Utility function for conditionally joining classNames
 * This is a simplified version - in a real project you might use tailwind-merge
 * @param inputs - Class values to merge
 * @returns Merged className string
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}`),
      createFile('formatters.ts', `/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - Currency code (default: 'USD')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

/**
 * Format a date in a human-readable way
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

/**
 * Format a number with thousand separators
 * @param num - Number to format
 * @returns Formatted number string
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num)
}

/**
 * Truncate text to a specified length
 * @param text - Text to truncate
 * @param length - Maximum length
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, length: number): string {
  if (text.length <= length) return text
  return text.slice(0, length) + '...'
}`),
    ]),
    createFolder('types', [
      createFile('index.ts', `// Common types used throughout the application

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export type Theme = 'light' | 'dark' | 'system'

export type Status = 'idle' | 'loading' | 'success' | 'error'`),
    ]),
  ]),
])

// Modern React + TypeScript + Vite template
const reactViteTemplate: FileNode = createFolder('react-vite-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('tsconfig.node.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('vite.svg'),
  ]),
  createFolder('src', [
    createFile('main.tsx'),
    createFile('App.tsx'),
    createFile('App.css'),
    createFile('index.css'),
    createFile('vite-env.d.ts'),
    createFolder('components', [
      createFile('Button.tsx'),
      createFile('Header.tsx'),
    ]),
    createFolder('hooks', [
      createFile('useLocalStorage.ts'),
    ]),
    createFolder('utils', [
      createFile('helpers.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
])

// Next.js 14 App Router template
const nextjsTemplate: FileNode = createFolder('nextjs-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('next.config.js'),
  createFile('tsconfig.json'),
  createFile('tailwind.config.js'),
  createFile('postcss.config.js'),
  createFile('README.md'),
  createFolder('app', [
    createFile('layout.tsx'),
    createFile('page.tsx'),
    createFile('globals.css'),
    createFolder('api', [
      createFolder('users', [
        createFile('route.ts'),
      ]),
    ]),
    createFolder('components', [
      createFile('Navigation.tsx'),
      createFile('Footer.tsx'),
    ]),
  ]),
  createFolder('lib', [
    createFile('utils.ts'),
    createFile('db.ts'),
  ]),
  createFolder('public', [
    createFile('next.svg'),
    createFile('vercel.svg'),
  ]),
])

// Express.js + TypeScript API template
const expressApiTemplate: FileNode = createFolder('express-api', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('tsconfig.json'),
  createFile('nodemon.json'),
  createFile('README.md'),
  createFile('.env.example'),
  createFolder('src', [
    createFile('app.ts'),
    createFile('server.ts'),
    createFolder('controllers', [
      createFile('userController.ts'),
      createFile('authController.ts'),
    ]),
    createFolder('middleware', [
      createFile('auth.ts'),
      createFile('errorHandler.ts'),
      createFile('validation.ts'),
    ]),
    createFolder('models', [
      createFile('User.ts'),
    ]),
    createFolder('routes', [
      createFile('users.ts'),
      createFile('auth.ts'),
    ]),
    createFolder('utils', [
      createFile('database.ts'),
      createFile('logger.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
  createFolder('tests', [
    createFile('setup.ts'),
    createFolder('unit', [
      createFile('user.test.ts'),
    ]),
    createFolder('integration', [
      createFile('auth.test.ts'),
    ]),
  ]),
])

// Vue 3 + Composition API template
const vue3Template: FileNode = createFolder('vue3-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('favicon.ico'),
  ]),
  createFolder('src', [
    createFile('main.ts'),
    createFile('App.vue'),
    createFile('style.css'),
    createFolder('components', [
      createFile('HelloWorld.vue'),
      createFile('TheHeader.vue'),
    ]),
    createFolder('composables', [
      createFile('useCounter.ts'),
    ]),
    createFolder('router', [
      createFile('index.ts'),
    ]),
    createFolder('stores', [
      createFile('counter.ts'),
    ]),
    createFolder('views', [
      createFile('HomeView.vue'),
      createFile('AboutView.vue'),
    ]),
  ]),
])

// Python FastAPI template
const fastApiTemplate: FileNode = createFolder('fastapi-app', [
  createFile('.gitignore'),
  createFile('requirements.txt'),
  createFile('pyproject.toml'),
  createFile('README.md'),
  createFile('.env.example'),
  createFile('main.py'),
  createFolder('app', [
    createFile('__init__.py'),
    createFile('main.py'),
    createFolder('api', [
      createFile('__init__.py'),
      createFolder('v1', [
        createFile('__init__.py'),
        createFile('endpoints.py'),
      ]),
    ]),
    createFolder('core', [
      createFile('__init__.py'),
      createFile('config.py'),
      createFile('security.py'),
    ]),
    createFolder('models', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('schemas', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('crud', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
  ]),
  createFolder('tests', [
    createFile('__init__.py'),
    createFile('test_main.py'),
  ]),
])

// Full-stack MERN template
const mernTemplate: FileNode = createFolder('mern-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('README.md'),
  createFolder('client', [
    createFile('package.json'),
    createFile('vite.config.ts'),
    createFile('tsconfig.json'),
    createFile('index.html'),
    createFolder('src', [
      createFile('main.tsx'),
      createFile('App.tsx'),
      createFolder('components', []),
      createFolder('pages', []),
      createFolder('hooks', []),
      createFolder('services', []),
    ]),
  ]),
  createFolder('server', [
    createFile('package.json'),
    createFile('tsconfig.json'),
    createFolder('src', [
      createFile('app.ts'),
      createFile('server.ts'),
      createFolder('controllers', []),
      createFolder('models', []),
      createFolder('routes', []),
      createFolder('middleware', []),
    ]),
  ]),
])

export const projectTemplates: ProjectTemplate[] = [
  {
    id: 'getting-started',
    name: '🚀 Getting Started Template',
    description: 'Perfect for beginners! Complete React + TypeScript + Tailwind CSS setup with examples, documentation, and best practices. Start here if you\'re new to modern web development.',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Tailwind', 'Beginner', 'Tutorial', 'Examples'],
    structure: gettingStartedTemplate,
    popularity: 100,
    lastUpdated: '2024-01-20',
    author: 'ProjectForge Team',
    downloads: 25000,
  },
  {
    id: 'react-vite-ts',
    name: 'React + Vite + TypeScript',
    description: 'Modern React application with Vite, TypeScript, and essential tooling',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Vite', 'Modern'],
    structure: reactViteTemplate,
    popularity: 95,
    lastUpdated: '2024-01-15',
    author: 'ProjectForge',
    downloads: 15420,
  },
  {
    id: 'nextjs-14-app',
    name: 'Next.js 14 App Router',
    description: 'Next.js 14 with App Router, TypeScript, and Tailwind CSS',
    category: 'Full-stack',
    tags: ['Next.js', 'React', 'TypeScript', 'Tailwind', 'App Router'],
    structure: nextjsTemplate,
    popularity: 92,
    lastUpdated: '2024-01-10',
    author: 'ProjectForge',
    downloads: 12350,
  },
  {
    id: 'express-api-ts',
    name: 'Express.js API + TypeScript',
    description: 'RESTful API with Express.js, TypeScript, and comprehensive testing',
    category: 'Backend',
    tags: ['Express.js', 'TypeScript', 'API', 'Testing'],
    structure: expressApiTemplate,
    popularity: 88,
    lastUpdated: '2024-01-12',
    author: 'ProjectForge',
    downloads: 9870,
  },
  {
    id: 'vue3-composition',
    name: 'Vue 3 + Composition API',
    description: 'Vue 3 application with Composition API, TypeScript, and Pinia',
    category: 'Frontend',
    tags: ['Vue.js', 'TypeScript', 'Composition API', 'Pinia'],
    structure: vue3Template,
    popularity: 85,
    lastUpdated: '2024-01-08',
    author: 'ProjectForge',
    downloads: 7650,
  },
  {
    id: 'fastapi-python',
    name: 'FastAPI + Python',
    description: 'High-performance Python API with FastAPI, async support, and auto docs',
    category: 'Backend',
    tags: ['Python', 'FastAPI', 'Async', 'API'],
    structure: fastApiTemplate,
    popularity: 82,
    lastUpdated: '2024-01-05',
    author: 'ProjectForge',
    downloads: 6420,
  },
  {
    id: 'mern-fullstack',
    name: 'MERN Full-stack',
    description: 'Complete MERN stack application with MongoDB, Express, React, and Node.js',
    category: 'Full-stack',
    tags: ['MongoDB', 'Express.js', 'React', 'Node.js', 'Full-stack'],
    structure: mernTemplate,
    popularity: 90,
    lastUpdated: '2024-01-14',
    author: 'ProjectForge',
    downloads: 11200,
  },
]

export const templateCategories = [
  'All',
  'Frontend',
  'Backend',
  'Full-stack',
  'Mobile',
  'Desktop',
  'CLI',
  'Library',
]

export const popularTags = [
  'React',
  'TypeScript',
  'Next.js',
  'Vue.js',
  'Express.js',
  'FastAPI',
  'Python',
  'Node.js',
  'MongoDB',
  'PostgreSQL',
  'Tailwind',
  'API',
  'Testing',
  'Modern',
]
