import { ProjectTemplate } from '../store/useAppStore'
import { FileNode } from '../types'

// Helper function to create file nodes
const createFile = (name: string, content?: string): FileNode => ({
  name,
  type: 'file',
  content: content || `// ${name}\n// Generated by ProjectForge\n`,
})

const createFolder = (name: string, children: FileNode[] = []): FileNode => ({
  name,
  type: 'directory',
  children,
})

// Modern React + TypeScript + Vite template
const reactViteTemplate: FileNode = createFolder('react-vite-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('tsconfig.node.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('vite.svg'),
  ]),
  createFolder('src', [
    createFile('main.tsx'),
    createFile('App.tsx'),
    createFile('App.css'),
    createFile('index.css'),
    createFile('vite-env.d.ts'),
    createFolder('components', [
      createFile('Button.tsx'),
      createFile('Header.tsx'),
    ]),
    createFolder('hooks', [
      createFile('useLocalStorage.ts'),
    ]),
    createFolder('utils', [
      createFile('helpers.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
])

// Next.js 14 App Router template
const nextjsTemplate: FileNode = createFolder('nextjs-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('next.config.js'),
  createFile('tsconfig.json'),
  createFile('tailwind.config.js'),
  createFile('postcss.config.js'),
  createFile('README.md'),
  createFolder('app', [
    createFile('layout.tsx'),
    createFile('page.tsx'),
    createFile('globals.css'),
    createFolder('api', [
      createFolder('users', [
        createFile('route.ts'),
      ]),
    ]),
    createFolder('components', [
      createFile('Navigation.tsx'),
      createFile('Footer.tsx'),
    ]),
  ]),
  createFolder('lib', [
    createFile('utils.ts'),
    createFile('db.ts'),
  ]),
  createFolder('public', [
    createFile('next.svg'),
    createFile('vercel.svg'),
  ]),
])

// Express.js + TypeScript API template
const expressApiTemplate: FileNode = createFolder('express-api', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('tsconfig.json'),
  createFile('nodemon.json'),
  createFile('README.md'),
  createFile('.env.example'),
  createFolder('src', [
    createFile('app.ts'),
    createFile('server.ts'),
    createFolder('controllers', [
      createFile('userController.ts'),
      createFile('authController.ts'),
    ]),
    createFolder('middleware', [
      createFile('auth.ts'),
      createFile('errorHandler.ts'),
      createFile('validation.ts'),
    ]),
    createFolder('models', [
      createFile('User.ts'),
    ]),
    createFolder('routes', [
      createFile('users.ts'),
      createFile('auth.ts'),
    ]),
    createFolder('utils', [
      createFile('database.ts'),
      createFile('logger.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
  createFolder('tests', [
    createFile('setup.ts'),
    createFolder('unit', [
      createFile('user.test.ts'),
    ]),
    createFolder('integration', [
      createFile('auth.test.ts'),
    ]),
  ]),
])

// Vue 3 + Composition API template
const vue3Template: FileNode = createFolder('vue3-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('favicon.ico'),
  ]),
  createFolder('src', [
    createFile('main.ts'),
    createFile('App.vue'),
    createFile('style.css'),
    createFolder('components', [
      createFile('HelloWorld.vue'),
      createFile('TheHeader.vue'),
    ]),
    createFolder('composables', [
      createFile('useCounter.ts'),
    ]),
    createFolder('router', [
      createFile('index.ts'),
    ]),
    createFolder('stores', [
      createFile('counter.ts'),
    ]),
    createFolder('views', [
      createFile('HomeView.vue'),
      createFile('AboutView.vue'),
    ]),
  ]),
])

// Python FastAPI template
const fastApiTemplate: FileNode = createFolder('fastapi-app', [
  createFile('.gitignore'),
  createFile('requirements.txt'),
  createFile('pyproject.toml'),
  createFile('README.md'),
  createFile('.env.example'),
  createFile('main.py'),
  createFolder('app', [
    createFile('__init__.py'),
    createFile('main.py'),
    createFolder('api', [
      createFile('__init__.py'),
      createFolder('v1', [
        createFile('__init__.py'),
        createFile('endpoints.py'),
      ]),
    ]),
    createFolder('core', [
      createFile('__init__.py'),
      createFile('config.py'),
      createFile('security.py'),
    ]),
    createFolder('models', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('schemas', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('crud', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
  ]),
  createFolder('tests', [
    createFile('__init__.py'),
    createFile('test_main.py'),
  ]),
])

// Full-stack MERN template
const mernTemplate: FileNode = createFolder('mern-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('README.md'),
  createFolder('client', [
    createFile('package.json'),
    createFile('vite.config.ts'),
    createFile('tsconfig.json'),
    createFile('index.html'),
    createFolder('src', [
      createFile('main.tsx'),
      createFile('App.tsx'),
      createFolder('components', []),
      createFolder('pages', []),
      createFolder('hooks', []),
      createFolder('services', []),
    ]),
  ]),
  createFolder('server', [
    createFile('package.json'),
    createFile('tsconfig.json'),
    createFolder('src', [
      createFile('app.ts'),
      createFile('server.ts'),
      createFolder('controllers', []),
      createFolder('models', []),
      createFolder('routes', []),
      createFolder('middleware', []),
    ]),
  ]),
])

export const projectTemplates: ProjectTemplate[] = [
  {
    id: 'react-vite-ts',
    name: 'React + Vite + TypeScript',
    description: 'Modern React application with Vite, TypeScript, and essential tooling',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Vite', 'Modern'],
    structure: reactViteTemplate,
    popularity: 95,
    lastUpdated: '2024-01-15',
    author: 'ProjectForge',
    downloads: 15420,
  },
  {
    id: 'nextjs-14-app',
    name: 'Next.js 14 App Router',
    description: 'Next.js 14 with App Router, TypeScript, and Tailwind CSS',
    category: 'Full-stack',
    tags: ['Next.js', 'React', 'TypeScript', 'Tailwind', 'App Router'],
    structure: nextjsTemplate,
    popularity: 92,
    lastUpdated: '2024-01-10',
    author: 'ProjectForge',
    downloads: 12350,
  },
  {
    id: 'express-api-ts',
    name: 'Express.js API + TypeScript',
    description: 'RESTful API with Express.js, TypeScript, and comprehensive testing',
    category: 'Backend',
    tags: ['Express.js', 'TypeScript', 'API', 'Testing'],
    structure: expressApiTemplate,
    popularity: 88,
    lastUpdated: '2024-01-12',
    author: 'ProjectForge',
    downloads: 9870,
  },
  {
    id: 'vue3-composition',
    name: 'Vue 3 + Composition API',
    description: 'Vue 3 application with Composition API, TypeScript, and Pinia',
    category: 'Frontend',
    tags: ['Vue.js', 'TypeScript', 'Composition API', 'Pinia'],
    structure: vue3Template,
    popularity: 85,
    lastUpdated: '2024-01-08',
    author: 'ProjectForge',
    downloads: 7650,
  },
  {
    id: 'fastapi-python',
    name: 'FastAPI + Python',
    description: 'High-performance Python API with FastAPI, async support, and auto docs',
    category: 'Backend',
    tags: ['Python', 'FastAPI', 'Async', 'API'],
    structure: fastApiTemplate,
    popularity: 82,
    lastUpdated: '2024-01-05',
    author: 'ProjectForge',
    downloads: 6420,
  },
  {
    id: 'mern-fullstack',
    name: 'MERN Full-stack',
    description: 'Complete MERN stack application with MongoDB, Express, React, and Node.js',
    category: 'Full-stack',
    tags: ['MongoDB', 'Express.js', 'React', 'Node.js', 'Full-stack'],
    structure: mernTemplate,
    popularity: 90,
    lastUpdated: '2024-01-14',
    author: 'ProjectForge',
    downloads: 11200,
  },
]

export const templateCategories = [
  'All',
  'Frontend',
  'Backend',
  'Full-stack',
  'Mobile',
  'Desktop',
  'CLI',
  'Library',
]

export const popularTags = [
  'React',
  'TypeScript',
  'Next.js',
  'Vue.js',
  'Express.js',
  'FastAPI',
  'Python',
  'Node.js',
  'MongoDB',
  'PostgreSQL',
  'Tailwind',
  'API',
  'Testing',
  'Modern',
]
