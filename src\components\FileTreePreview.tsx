import React, { useState } from 'react';
import { ChevronDown, ChevronRight, File, Folder, FileEdit } from 'lucide-react';
import { FileNode } from '../types';

interface FileTreeItemProps {
  node: FileNode;
  level: number;
  onSelectFile: (node: FileNode) => void;
}

interface FileTreePreviewProps {
  structure: FileNode;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({ node, level, onSelectFile }) => {
  const [isOpen, setIsOpen] = useState(level < 2);
  const hasChildren = node.children && node.children.length > 0;
  
  const toggleOpen = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelectFile = () => {
    if (!hasChildren) {
      onSelectFile(node);
    }
  };

  return (
    <div className="select-none">
      <div 
        className={`flex items-center py-1 px-2 rounded hover:bg-gray-100 cursor-pointer ${!hasChildren ? 'hover:text-blue-700' : ''}`}
        onClick={hasChildren ? toggleOpen : handleSelectFile}
        style={{ paddingLeft: `${level * 16}px` }}
      >
        {hasChildren ? (
          isOpen ? <ChevronDown size={16} className="mr-1 text-gray-500" /> 
                : <ChevronRight size={16} className="mr-1 text-gray-500" />
        ) : (
          <span className="w-4 mr-1"></span>
        )}
        
        {hasChildren ? (
          <Folder size={16} className="mr-2 text-yellow-500" />
        ) : (
          <File size={16} className="mr-2 text-blue-500" />
        )}
        
        <span className="text-sm">{node.name}</span>
        
        {!hasChildren && node.content && (
          <FileEdit size={14} className="ml-2 text-gray-400" />
        )}
      </div>
      
      {isOpen && hasChildren && node.children && (
        <div>
          {node.children.map((child, index) => (
            <FileTreeItem 
              key={`${child.name}-${index}`}
              node={child}
              level={level + 1}
              onSelectFile={onSelectFile}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const FileTreePreview: React.FC<FileTreePreviewProps> = ({ structure }) => {
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  
  const handleSelectFile = (node: FileNode) => {
    setSelectedFile(node);
  };
  
  const handleClose = () => {
    setSelectedFile(null);
  };

  return (
    <div className="text-gray-800">
      {selectedFile ? (
        <div className="h-full">
          <div className="flex justify-between items-center mb-3 pb-2 border-b">
            <div className="flex items-center">
              <File size={16} className="mr-2 text-blue-500" />
              <h3 className="font-medium">{selectedFile.name}</h3>
            </div>
            <button 
              onClick={handleClose}
              className="text-sm px-2 py-1 text-gray-600 hover:text-gray-800"
            >
              Back to tree
            </button>
          </div>
          
          <div className="h-[500px] overflow-auto">
            <pre className="text-xs bg-gray-50 p-4 rounded border font-mono whitespace-pre-wrap">
              {selectedFile.content || '// No content generated for this file yet'}
            </pre>
          </div>
        </div>
      ) : (
        <FileTreeItem 
          node={structure} 
          level={0} 
          onSelectFile={handleSelectFile} 
        />
      )}
    </div>
  );
};

export default FileTreePreview;