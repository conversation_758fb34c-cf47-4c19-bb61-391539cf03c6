import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON>rkles, 
  Code, 
  Database, 
  Globe, 
  Smartphone, 
  Server,
  CheckCircle,
  Clock,
  User,
  Download,
  Lightbulb,
  Zap
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { FileNode, Project } from '../types'

interface ProjectTypeOption {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  features: string[]
}

const projectTypeOptions: ProjectTypeOption[] = [
  {
    id: 'web-app',
    name: 'Web Application',
    description: 'Modern React/Vue/Angular application with responsive design',
    icon: Globe,
    color: 'blue',
    features: ['Responsive Design', 'Modern Framework', 'State Management', 'API Integration']
  },
  {
    id: 'mobile-app',
    name: 'Mobile App',
    description: 'Cross-platform mobile application with native performance',
    icon: Smartphone,
    color: 'green',
    features: ['Cross-platform', 'Native Performance', 'Push Notifications', 'Offline Support']
  },
  {
    id: 'api-backend',
    name: 'API Backend',
    description: 'RESTful API with database integration and authentication',
    icon: Server,
    color: 'purple',
    features: ['RESTful API', 'Database Integration', 'Authentication', 'Documentation']
  },
  {
    id: 'full-stack',
    name: 'Full-stack App',
    description: 'Complete application with frontend, backend, and database',
    icon: Code,
    color: 'orange',
    features: ['Frontend & Backend', 'Database', 'Authentication', 'Deployment Ready']
  },
  {
    id: 'data-project',
    name: 'Data Project',
    description: 'Data analysis, visualization, or machine learning project',
    icon: Database,
    color: 'indigo',
    features: ['Data Processing', 'Visualization', 'Analytics', 'ML Integration']
  }
]

interface GeneratedProject {
  name: string
  description: string
  type: string
  techStack: string[]
  features: string[]
  structure: FileNode
  estimatedTime: string
}

const AIProjectGenerator: React.FC = () => {
  const { setCurrentProject, incrementFeatureUsed } = useAppStore()
  const [projectType, setProjectType] = useState('')
  const [projectName, setProjectName] = useState('')
  const [projectDescription, setProjectDescription] = useState('')
  const [techPreferences, setTechPreferences] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedProject, setGeneratedProject] = useState<GeneratedProject | null>(null)

  const handleGenerate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!projectType || !projectName) return

    setIsGenerating(true)
    incrementFeatureUsed('ai-generation')

    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 2000))

    const selectedType = projectTypeOptions.find(opt => opt.id === projectType)
    
    // Generate a mock project structure
    const mockProject: GeneratedProject = {
      name: projectName,
      description: projectDescription || `AI-generated ${selectedType?.name} project`,
      type: projectType,
      techStack: getTechStackForType(projectType),
      features: selectedType?.features || [],
      structure: generateProjectStructure(projectType, projectName),
      estimatedTime: getEstimatedTime(projectType)
    }

    setGeneratedProject(mockProject)
    setIsGenerating(false)
  }

  const getTechStackForType = (type: string): string[] => {
    const stacks: Record<string, string[]> = {
      'web-app': ['React', 'TypeScript', 'Tailwind CSS', 'Vite'],
      'mobile-app': ['React Native', 'TypeScript', 'Expo', 'AsyncStorage'],
      'api-backend': ['Node.js', 'Express', 'TypeScript', 'MongoDB'],
      'full-stack': ['React', 'Node.js', 'TypeScript', 'MongoDB', 'Express'],
      'data-project': ['Python', 'Pandas', 'Matplotlib', 'Jupyter']
    }
    return stacks[type] || ['JavaScript', 'HTML', 'CSS']
  }

  const getEstimatedTime = (type: string): string => {
    const times: Record<string, string> = {
      'web-app': '1-2 weeks',
      'mobile-app': '2-3 weeks',
      'api-backend': '1 week',
      'full-stack': '3-4 weeks',
      'data-project': '1-2 weeks'
    }
    return times[type] || '1 week'
  }

  const generateProjectStructure = (type: string, name: string): FileNode => {
    // This would be replaced with actual AI-generated structure
    return {
      name: name.toLowerCase().replace(/\s+/g, '-'),
      type: 'folder',
      children: [
        { name: 'README.md', type: 'file', content: `# ${name}\n\nAI-generated project structure.` },
        { name: 'package.json', type: 'file', content: '{}' },
        { 
          name: 'src', 
          type: 'folder', 
          children: [
            { name: 'index.ts', type: 'file', content: '// Entry point' },
            { name: 'components', type: 'folder', children: [] }
          ]
        }
      ]
    }
  }

  const handleUseProject = () => {
    if (!generatedProject) return

    const project: Project = {
      id: Date.now().toString(),
      name: generatedProject.name,
      description: generatedProject.description,
      structure: generatedProject.structure,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      tags: [generatedProject.type, 'ai-generated'],
      isStarred: false
    }

    setCurrentProject(project)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">AI Project Generator</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Describe your project idea and let AI generate a complete project structure with best practices, 
          modern tech stack, and ready-to-use code templates.
        </p>
      </div>

      <form onSubmit={handleGenerate} className="space-y-6">
        {/* Project Type */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Project Type *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {projectTypeOptions.map(option => {
              const Icon = option.icon
              return (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectType(option.id)}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectType === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Icon className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Project Details */}
        <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
          <div>
            <label htmlFor="projectName" className="block text-sm font-medium text-gray-700 mb-2">
              Project Name *
            </label>
            <input
              type="text"
              id="projectName"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              placeholder="My Awesome Project"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="projectDescription" className="block text-sm font-medium text-gray-700 mb-2">
              Project Description
            </label>
            <textarea
              id="projectDescription"
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder="Describe what your project should do, its main features, and any specific requirements..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <Button
          type="submit"
          disabled={!projectType || !projectName || isGenerating}
          className="w-full"
          size="lg"
        >
          <Sparkles className="h-5 w-5 mr-2" />
          {isGenerating ? 'Generating Project...' : 'Generate Project with AI'}
        </Button>
      </form>

      {/* Generated Project Info */}
      {generatedProject && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border p-4 sm:p-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900">Project Generated Successfully!</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Est. Time: {generatedProject.estimatedTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">{generatedProject.techStack.length} Technologies</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">{generatedProject.features.length} Features</span>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Tech Stack</h4>
              <div className="flex flex-wrap gap-2">
                {generatedProject.techStack.map(tech => (
                  <span key={tech} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
              <div className="flex flex-wrap gap-2">
                {generatedProject.features.map(feature => (
                  <span key={feature} className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <Button onClick={handleUseProject} variant="gradient">
              <Download className="h-4 w-4 mr-2" />
              Use This Project
            </Button>
            <Button variant="outline" onClick={() => setGeneratedProject(null)}>
              Generate Another
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default AIProjectGenerator
