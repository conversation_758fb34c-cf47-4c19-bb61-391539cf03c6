import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Sparkles,
  Code,
  Database,
  Globe,
  Smartphone,
  Server,
  CheckCircle,
  Clock,
  Download,
  Zap,
  ArrowRight,
  ArrowLeft,
  Shield,
  Settings,
  Layers
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { FileNode, Project } from '../types'

// Technology Options
const frontendOptions = [
  { id: 'react', name: 'React', icon: '⚛️', description: 'Popular library for building user interfaces' },
  { id: 'vue', name: 'Vue.js', icon: '🟢', description: 'Progressive framework for building UIs' },
  { id: 'angular', name: 'Angular', icon: '🔺', description: 'Platform for building mobile and desktop apps' },
  { id: 'svelte', name: '<PERSON>vel<PERSON>', icon: '🔥', description: 'Compile-time optimized framework' },
  { id: 'next', name: 'Next.js', icon: '▲', description: 'React framework with SSR and SSG' },
  { id: 'nuxt', name: 'Nuxt.js', icon: '💚', description: 'Vue.js framework with SSR' },
  { id: 'vanilla', name: 'Vanilla JS', icon: '🟨', description: 'Pure JavaScript without frameworks' }
]

const backendOptions = [
  { id: 'node', name: 'Node.js', icon: '🟢', description: 'JavaScript runtime for server-side development' },
  { id: 'express', name: 'Express.js', icon: '🚂', description: 'Fast, minimalist web framework for Node.js' },
  { id: 'fastapi', name: 'FastAPI', icon: '⚡', description: 'Modern Python web framework' },
  { id: 'django', name: 'Django', icon: '🎸', description: 'High-level Python web framework' },
  { id: 'flask', name: 'Flask', icon: '🌶️', description: 'Lightweight Python web framework' },
  { id: 'spring', name: 'Spring Boot', icon: '🍃', description: 'Java framework for enterprise applications' },
  { id: 'laravel', name: 'Laravel', icon: '🔴', description: 'PHP framework for web artisans' },
  { id: 'rails', name: 'Ruby on Rails', icon: '💎', description: 'Web application framework written in Ruby' }
]

const databaseOptions = [
  { id: 'mongodb', name: 'MongoDB', icon: '🍃', description: 'NoSQL document database' },
  { id: 'postgresql', name: 'PostgreSQL', icon: '🐘', description: 'Advanced open source relational database' },
  { id: 'mysql', name: 'MySQL', icon: '🐬', description: 'Popular open source relational database' },
  { id: 'sqlite', name: 'SQLite', icon: '🪶', description: 'Lightweight embedded database' },
  { id: 'redis', name: 'Redis', icon: '🔴', description: 'In-memory data structure store' },
  { id: 'firebase', name: 'Firebase', icon: '🔥', description: 'Google\'s mobile and web development platform' },
  { id: 'supabase', name: 'Supabase', icon: '⚡', description: 'Open source Firebase alternative' }
]

const authOptions = [
  { id: 'jwt', name: 'JWT', icon: '🔑', description: 'JSON Web Tokens for stateless authentication' },
  { id: 'oauth', name: 'OAuth 2.0', icon: '🔐', description: 'Industry standard for authorization' },
  { id: 'firebase-auth', name: 'Firebase Auth', icon: '🔥', description: 'Google\'s authentication service' },
  { id: 'auth0', name: 'Auth0', icon: '🛡️', description: 'Identity platform for developers' },
  { id: 'passport', name: 'Passport.js', icon: '📘', description: 'Authentication middleware for Node.js' },
  { id: 'nextauth', name: 'NextAuth.js', icon: '🔒', description: 'Authentication for Next.js' }
]

const deploymentOptions = [
  { id: 'vercel', name: 'Vercel', icon: '▲', description: 'Platform for frontend frameworks' },
  { id: 'netlify', name: 'Netlify', icon: '🌐', description: 'Platform for modern web projects' },
  { id: 'heroku', name: 'Heroku', icon: '💜', description: 'Cloud platform as a service' },
  { id: 'aws', name: 'AWS', icon: '☁️', description: 'Amazon Web Services cloud platform' },
  { id: 'docker', name: 'Docker', icon: '🐳', description: 'Containerization platform' },
  { id: 'railway', name: 'Railway', icon: '🚄', description: 'Modern deployment platform' }
]

interface ProjectTypeOption {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  features: string[]
}

const projectTypeOptions: ProjectTypeOption[] = [
  {
    id: 'web-app',
    name: 'Web Application',
    description: 'Modern React/Vue/Angular application with responsive design',
    icon: Globe,
    color: 'blue',
    features: ['Responsive Design', 'Modern Framework', 'State Management', 'API Integration']
  },
  {
    id: 'mobile-app',
    name: 'Mobile App',
    description: 'Cross-platform mobile application with native performance',
    icon: Smartphone,
    color: 'green',
    features: ['Cross-platform', 'Native Performance', 'Push Notifications', 'Offline Support']
  },
  {
    id: 'api-backend',
    name: 'API Backend',
    description: 'RESTful API with database integration and authentication',
    icon: Server,
    color: 'purple',
    features: ['RESTful API', 'Database Integration', 'Authentication', 'Documentation']
  },
  {
    id: 'full-stack',
    name: 'Full-stack App',
    description: 'Complete application with frontend, backend, and database',
    icon: Code,
    color: 'orange',
    features: ['Frontend & Backend', 'Database', 'Authentication', 'Deployment Ready']
  },
  {
    id: 'data-project',
    name: 'Data Project',
    description: 'Data analysis, visualization, or machine learning project',
    icon: Database,
    color: 'indigo',
    features: ['Data Processing', 'Visualization', 'Analytics', 'ML Integration']
  }
]

interface GeneratedProject {
  name: string
  description: string
  type: string
  techStack: string[]
  features: string[]
  structure: FileNode
  estimatedTime: string
}

interface ProjectConfig {
  name: string
  description: string
  type: string
  frontend: string
  backend: string
  database: string
  auth: string
  deployment: string
  features: string[]
}

const AIProjectGenerator: React.FC = () => {
  const { setCurrentProject, incrementFeatureUsed } = useAppStore()

  // Wizard state
  const [currentStep, setCurrentStep] = useState(0)
  const [projectConfig, setProjectConfig] = useState<ProjectConfig>({
    name: '',
    description: '',
    type: '',
    frontend: '',
    backend: '',
    database: '',
    auth: '',
    deployment: '',
    features: []
  })

  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedProject, setGeneratedProject] = useState<GeneratedProject | null>(null)

  const steps = [
    { id: 'project-info', title: 'Project Info', icon: Settings },
    { id: 'project-type', title: 'Project Type', icon: Layers },
    { id: 'frontend', title: 'Frontend', icon: Globe },
    { id: 'backend', title: 'Backend', icon: Server },
    { id: 'database', title: 'Database', icon: Database },
    { id: 'auth', title: 'Authentication', icon: Shield },
    { id: 'deployment', title: 'Deployment', icon: Zap },
    { id: 'review', title: 'Review', icon: CheckCircle }
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleGenerate = async () => {
    setIsGenerating(true)
    incrementFeatureUsed('ai-generation')

    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 2000))

    const selectedType = projectTypeOptions.find(opt => opt.id === projectConfig.type)

    // Build tech stack from selections
    const techStack = []
    if (projectConfig.frontend) techStack.push(frontendOptions.find(f => f.id === projectConfig.frontend)?.name || projectConfig.frontend)
    if (projectConfig.backend) techStack.push(backendOptions.find(b => b.id === projectConfig.backend)?.name || projectConfig.backend)
    if (projectConfig.database) techStack.push(databaseOptions.find(d => d.id === projectConfig.database)?.name || projectConfig.database)
    if (projectConfig.auth) techStack.push(authOptions.find(a => a.id === projectConfig.auth)?.name || projectConfig.auth)
    if (projectConfig.deployment) techStack.push(deploymentOptions.find(d => d.id === projectConfig.deployment)?.name || projectConfig.deployment)

    // Generate a mock project structure
    const mockProject: GeneratedProject = {
      name: projectConfig.name,
      description: projectConfig.description || `AI-generated ${selectedType?.name} project`,
      type: projectConfig.type,
      techStack,
      features: selectedType?.features || [],
      structure: generateProjectStructure(projectConfig.type, projectConfig.name),
      estimatedTime: getEstimatedTime(projectConfig.type)
    }

    setGeneratedProject(mockProject)
    setIsGenerating(false)
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: return projectConfig.name.trim() !== ''
      case 1: return projectConfig.type !== ''
      case 2: return projectConfig.frontend !== '' || projectConfig.type === 'api-backend'
      case 3: return projectConfig.backend !== '' || projectConfig.type === 'web-app'
      case 4: return true // Database is optional
      case 5: return true // Auth is optional
      case 6: return true // Deployment is optional
      case 7: return true // Review step
      default: return false
    }
  }

  const getTechStackForType = (type: string): string[] => {
    const stacks: Record<string, string[]> = {
      'web-app': ['React', 'TypeScript', 'Tailwind CSS', 'Vite'],
      'mobile-app': ['React Native', 'TypeScript', 'Expo', 'AsyncStorage'],
      'api-backend': ['Node.js', 'Express', 'TypeScript', 'MongoDB'],
      'full-stack': ['React', 'Node.js', 'TypeScript', 'MongoDB', 'Express'],
      'data-project': ['Python', 'Pandas', 'Matplotlib', 'Jupyter']
    }
    return stacks[type] || ['JavaScript', 'HTML', 'CSS']
  }

  const getEstimatedTime = (type: string): string => {
    const times: Record<string, string> = {
      'web-app': '1-2 weeks',
      'mobile-app': '2-3 weeks',
      'api-backend': '1 week',
      'full-stack': '3-4 weeks',
      'data-project': '1-2 weeks'
    }
    return times[type] || '1 week'
  }

  const generateProjectStructure = (type: string, name: string): FileNode => {
    // This would be replaced with actual AI-generated structure
    return {
      name: name.toLowerCase().replace(/\s+/g, '-'),
      type: 'folder',
      children: [
        { name: 'README.md', type: 'file', content: `# ${name}\n\nAI-generated project structure.` },
        { name: 'package.json', type: 'file', content: '{}' },
        { 
          name: 'src', 
          type: 'folder', 
          children: [
            { name: 'index.ts', type: 'file', content: '// Entry point' },
            { name: 'components', type: 'folder', children: [] }
          ]
        }
      ]
    }
  }

  const handleUseProject = () => {
    if (!generatedProject) return

    const project: Project = {
      id: Date.now().toString(),
      name: generatedProject.name,
      description: generatedProject.description,
      structure: generatedProject.structure,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      tags: [generatedProject.type, 'ai-generated'],
      isStarred: false
    }

    setCurrentProject(project)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Project Info
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="projectName" className="block text-sm font-medium text-gray-700 mb-2">
                Project Name *
              </label>
              <input
                type="text"
                id="projectName"
                value={projectConfig.name}
                onChange={(e) => setProjectConfig({...projectConfig, name: e.target.value})}
                placeholder="My Awesome Project"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div>
              <label htmlFor="projectDescription" className="block text-sm font-medium text-gray-700 mb-2">
                Project Description
              </label>
              <textarea
                id="projectDescription"
                value={projectConfig.description}
                onChange={(e) => setProjectConfig({...projectConfig, description: e.target.value})}
                placeholder="Describe what your project should do, its main features, and any specific requirements..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        )

      case 1: // Project Type
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {projectTypeOptions.map(option => {
              const Icon = option.icon
              return (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, type: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.type === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Icon className="h-6 w-6 text-blue-500 mt-1" />
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        )

      case 2: // Frontend
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Choose your frontend framework (skip if building API-only)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {frontendOptions.map(option => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, frontend: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.frontend === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 3: // Backend
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Choose your backend framework (skip if building frontend-only)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {backendOptions.map(option => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, backend: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.backend === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 4: // Database
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Choose your database (optional)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {databaseOptions.map(option => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, database: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.database === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 5: // Authentication
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Choose your authentication method (optional)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {authOptions.map(option => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, auth: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.auth === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 6: // Deployment
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Choose your deployment platform (optional)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {deploymentOptions.map(option => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setProjectConfig({...projectConfig, deployment: option.id})}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    projectConfig.deployment === option.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{option.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 7: // Review
        return (
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-700">Project Name:</span>
                  <p className="text-gray-900">{projectConfig.name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-700">Project Type:</span>
                  <p className="text-gray-900">{projectTypeOptions.find(t => t.id === projectConfig.type)?.name}</p>
                </div>
                {projectConfig.frontend && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Frontend:</span>
                    <p className="text-gray-900">{frontendOptions.find(f => f.id === projectConfig.frontend)?.name}</p>
                  </div>
                )}
                {projectConfig.backend && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Backend:</span>
                    <p className="text-gray-900">{backendOptions.find(b => b.id === projectConfig.backend)?.name}</p>
                  </div>
                )}
                {projectConfig.database && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Database:</span>
                    <p className="text-gray-900">{databaseOptions.find(d => d.id === projectConfig.database)?.name}</p>
                  </div>
                )}
                {projectConfig.auth && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Authentication:</span>
                    <p className="text-gray-900">{authOptions.find(a => a.id === projectConfig.auth)?.name}</p>
                  </div>
                )}
                {projectConfig.deployment && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Deployment:</span>
                    <p className="text-gray-900">{deploymentOptions.find(d => d.id === projectConfig.deployment)?.name}</p>
                  </div>
                )}
              </div>
              {projectConfig.description && (
                <div className="mt-4">
                  <span className="text-sm font-medium text-gray-700">Description:</span>
                  <p className="text-gray-900 mt-1">{projectConfig.description}</p>
                </div>
              )}
            </div>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="w-full"
              size="lg"
              variant="gradient"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              {isGenerating ? 'Generating Project...' : 'Generate Project with AI'}
            </Button>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">AI Project Generator</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Configure your project step by step and let AI generate a complete project structure with best practices,
          modern tech stack, and ready-to-use code templates.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = index === currentStep
            const isCompleted = index < currentStep

            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  isActive
                    ? 'border-blue-500 bg-blue-50 text-blue-600'
                    : isCompleted
                      ? 'border-green-500 bg-green-50 text-green-600'
                      : 'border-gray-300 bg-gray-50 text-gray-400'
                }`}>
                  <Icon className="h-5 w-5" />
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                }`}>
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {steps[currentStep].title}
        </h2>

        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        {currentStep < 7 && (
          <div className="flex justify-between mt-8">
            <Button
              onClick={handlePrevious}
              disabled={currentStep === 0}
              variant="outline"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              onClick={handleNext}
              disabled={!canProceed()}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Generated Project Info */}
      {generatedProject && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border p-4 sm:p-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900">Project Generated Successfully!</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Est. Time: {generatedProject.estimatedTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">{generatedProject.techStack.length} Technologies</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">{generatedProject.features.length} Features</span>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Tech Stack</h4>
              <div className="flex flex-wrap gap-2">
                {generatedProject.techStack.map(tech => (
                  <span key={tech} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
              <div className="flex flex-wrap gap-2">
                {generatedProject.features.map(feature => (
                  <span key={feature} className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <Button onClick={handleUseProject} variant="gradient">
              <Download className="h-4 w-4 mr-2" />
              Use This Project
            </Button>
            <Button variant="outline" onClick={() => setGeneratedProject(null)}>
              Generate Another
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default AIProjectGenerator
