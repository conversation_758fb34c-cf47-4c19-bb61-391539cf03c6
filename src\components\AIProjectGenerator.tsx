import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON><PERSON>les,
  Wand2,
  Brain,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Clock,
  Target,
  Code,
  Smartphone,
  Monitor,
  Server,
  Package,
  Terminal,
  Wifi,
  WifiOff
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { useAppStore } from '../store/useAppStore'
import { AIProjectService, AIProjectRequest } from '../services/aiService'
import { useToast } from './ui/Toast'
import { cn } from '../lib/utils'

const projectTypeOptions = [
  { id: 'web', name: 'Web Application', icon: Monitor, description: 'Frontend web applications' },
  { id: 'mobile', name: 'Mobile App', icon: Smartphone, description: 'iOS and Android applications' },
  { id: 'desktop', name: 'Desktop App', icon: Monitor, description: 'Cross-platform desktop applications' },
  { id: 'api', name: 'API/Backend', icon: Server, description: 'RESTful APIs and backend services' },
  { id: 'library', name: 'Library/Package', icon: Package, description: 'Reusable code libraries' },
  { id: 'cli', name: 'CLI Tool', icon: Terminal, description: 'Command-line applications' },
]

const technologyOptions = [
  'React', 'Vue.js', 'Angular', 'Svelte', 'Next.js', 'Nuxt.js',
  'TypeScript', 'JavaScript', 'Python', 'Node.js', 'Express.js',
  'FastAPI', 'Django', 'Flask', 'Go', 'Rust', 'Java', 'C#',
  'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'GraphQL', 'REST',
  'Tailwind CSS', 'Material-UI', 'Chakra UI', 'Bootstrap',
  'Docker', 'Kubernetes', 'AWS', 'Vercel', 'Netlify'
]

const featureOptions = [
  'Authentication', 'Database Integration', 'API Integration', 'Real-time Updates',
  'File Upload', 'Email Notifications', 'Payment Processing', 'Search Functionality',
  'Data Visualization', 'Responsive Design', 'PWA Features', 'Offline Support',
  'Testing Setup', 'CI/CD Pipeline', 'Error Handling', 'Logging', 'Caching',
  'State Management', 'Routing', 'Internationalization', 'Accessibility'
]

const AIProjectGenerator: React.FC = () => {
  const { setCurrentProject, setIsGenerating, incrementFeatureUsed } = useAppStore()
  const { toast } = useToast()

  const [formData, setFormData] = useState<AIProjectRequest>({
    description: '',
    projectType: 'web',
    technologies: [],
    features: [],
    complexity: 'medium',
    includeTests: true,
    includeDocumentation: true,
  })

  const [isGenerating, setLocalIsGenerating] = useState(false)
  const [generatedProject, setGeneratedProject] = useState<any>(null)
  const [aiServiceInfo, setAiServiceInfo] = useState<any>(null)
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking')

  // Check AI service availability on component mount
  useEffect(() => {
    const checkAIService = async () => {
      const serviceInfo = AIProjectService.getAIServiceInfo()
      setAiServiceInfo(serviceInfo)

      if (serviceInfo.available) {
        try {
          const connectionTest = await AIProjectService.testConnection()
          setConnectionStatus(connectionTest.success ? 'connected' : 'disconnected')

          if (!connectionTest.success) {
            toast({
              title: 'AI Service Warning',
              description: connectionTest.message,
              variant: 'warning',
            })
          }
        } catch (error) {
          setConnectionStatus('disconnected')
        }
      } else {
        setConnectionStatus('disconnected')
      }
    }

    checkAIService()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.description.trim()) {
      toast({
        title: 'Description Required',
        description: 'Please provide a description for your project',
        variant: 'warning',
      })
      return
    }

    if (formData.technologies.length === 0) {
      toast({
        title: 'Technologies Required',
        description: 'Please select at least one technology',
        variant: 'warning',
      })
      return
    }

    setLocalIsGenerating(true)
    setIsGenerating(true)
    incrementFeatureUsed('ai-generation')

    try {
      const result = await AIProjectService.generateProject(formData)
      setGeneratedProject(result)
      
      setCurrentProject({
        name: result.projectName,
        frontend: 'ai-generated',
        backend: null,
        database: null,
        structure: result.structure,
      })

      toast({
        title: 'Project Generated Successfully!',
        description: `Your ${result.projectName} project is ready`,
        variant: 'success',
      })
    } catch (error) {
      toast({
        title: 'Generation Failed',
        description: 'Failed to generate project. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLocalIsGenerating(false)
      setIsGenerating(false)
    }
  }

  const toggleTechnology = (tech: string) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.includes(tech)
        ? prev.technologies.filter(t => t !== tech)
        : [...prev.technologies, tech]
    }))
  }

  const toggleFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }))
  }

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6 border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Sparkles className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">AI Project Generator</h2>
              <p className="text-gray-600">Describe your project and let AI create the perfect structure</p>
            </div>
          </div>

          {/* AI Service Status */}
          <div className="flex items-center gap-2 text-sm">
            {connectionStatus === 'checking' && (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
                <span>Checking AI service...</span>
              </div>
            )}

            {connectionStatus === 'connected' && aiServiceInfo && (
              <div className="flex items-center gap-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span>Powered by {aiServiceInfo.provider}</span>
              </div>
            )}

            {connectionStatus === 'disconnected' && (
              <div className="flex items-center gap-2 text-orange-600">
                <WifiOff className="h-4 w-4" />
                <span>AI service unavailable</span>
              </div>
            )}
          </div>
        </div>

        {/* Service unavailable warning */}
        {connectionStatus === 'disconnected' && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 text-orange-800 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>
                AI service is currently unavailable. The generator will use a basic fallback system.
                {!aiServiceInfo?.available && ' Please configure the VITE_GEMINI_API_KEY environment variable.'}
              </span>
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Project Description */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Project Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Describe your project idea in detail. For example: 'A social media platform for developers to share code snippets and collaborate on projects'"
            className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            disabled={isGenerating}
          />
        </div>

        {/* Project Type */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Project Type *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {projectTypeOptions.map(option => {
              const Icon = option.icon
              return (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, projectType: option.id as any }))}
                  disabled={isGenerating}
                  className={cn(
                    'p-4 rounded-lg border-2 transition-all text-left',
                    formData.projectType === option.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-purple-300'
                  )}
                >
                  <Icon className="h-6 w-6 text-purple-600 mb-2" />
                  <h3 className="font-medium text-gray-900">{option.name}</h3>
                  <p className="text-sm text-gray-600">{option.description}</p>
                </button>
              )
            })}
          </div>
        </div>

        {/* Technologies */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Technologies * (Select at least one)
          </label>
          <div className="flex flex-wrap gap-2">
            {technologyOptions.map(tech => (
              <button
                key={tech}
                type="button"
                onClick={() => toggleTechnology(tech)}
                disabled={isGenerating}
                className={cn(
                  'px-3 py-2 rounded-md text-sm border transition-colors',
                  formData.technologies.includes(tech)
                    ? 'bg-purple-500 text-white border-purple-500'
                    : 'bg-white text-gray-600 border-gray-300 hover:border-purple-300'
                )}
              >
                {tech}
              </button>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Features (Optional)
          </label>
          <div className="flex flex-wrap gap-2">
            {featureOptions.map(feature => (
              <button
                key={feature}
                type="button"
                onClick={() => toggleFeature(feature)}
                disabled={isGenerating}
                className={cn(
                  'px-3 py-2 rounded-md text-sm border transition-colors',
                  formData.features.includes(feature)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-600 border-gray-300 hover:border-blue-300'
                )}
              >
                {feature}
              </button>
            ))}
          </div>
        </div>

        {/* Complexity and Options */}
        <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project Complexity
            </label>
            <select
              value={formData.complexity}
              onChange={(e) => setFormData(prev => ({ ...prev, complexity: e.target.value as any }))}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
            >
              <option value="simple">Simple - Basic functionality</option>
              <option value="medium">Medium - Standard features</option>
              <option value="complex">Complex - Advanced features</option>
            </select>
          </div>

          <div className="flex gap-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.includeTests}
                onChange={(e) => setFormData(prev => ({ ...prev, includeTests: e.target.checked }))}
                disabled={isGenerating}
                className="mr-2"
              />
              Include Testing Setup
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.includeDocumentation}
                onChange={(e) => setFormData(prev => ({ ...prev, includeDocumentation: e.target.checked }))}
                disabled={isGenerating}
                className="mr-2"
              />
              Include Documentation
            </label>
          </div>
        </div>

        {/* Generate Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          loading={isGenerating}
          disabled={isGenerating}
          className="w-full"
          leftIcon={<Wand2 className="h-5 w-5" />}
        >
          {isGenerating ? 'Generating Project...' : 'Generate Project with AI'}
        </Button>
      </form>

      {/* Generated Project Info */}
      {generatedProject && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border p-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900">Project Generated Successfully!</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Estimated Time: {generatedProject.estimatedTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Difficulty: {generatedProject.difficulty}</span>
            </div>
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">AI Generated</span>
            </div>
          </div>

          {generatedProject.recommendations.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                AI Recommendations:
              </h4>
              <ul className="space-y-1">
                {generatedProject.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                    <span className="text-yellow-500 mt-1">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      )}
    </div>
  )
}

export default AIProjectGenerator
