import React from 'react';
import { FileIcon } from 'lucide-react';
import Header from './components/Header';
import TechStackSelector from './components/TechStackSelector';
import CustomStructureInput from './components/CustomStructureInput';
import FileTreePreview from './components/FileTreePreview';
import DownloadSection from './components/DownloadSection';
import { useModeContext, ModeProvider } from './contexts/ModeContext';
import { useProjectContext, ProjectProvider } from './contexts/ProjectContext';

function MainContent() {
  const { mode } = useModeContext();
  const { project, isGenerating } = useProjectContext();

  return (
    <main className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          {mode === 'stack' ? (
            <TechStackSelector />
          ) : (
            <CustomStructureInput />
          )}
          
          {project.structure && !isGenerating && (
            <DownloadSection />
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6 h-[600px] overflow-auto">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">Project Preview</h2>
          {project.structure ? (
            <FileTreePreview structure={project.structure} />
          ) : (
            <div className="h-full flex flex-col items-center justify-center text-gray-400">
              <FileIcon size={64} strokeWidth={1} />
              <p className="mt-4 text-center">
                Your project structure will appear here once generated
              </p>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}

function App() {
  return (
    <ProjectProvider>
      <ModeProvider>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <MainContent />
        </div>
      </ModeProvider>
    </ProjectProvider>
  );
}

export default App;