import { FileNode } from '../types'

export interface AIProjectRequest {
  description: string
  projectType: 'web' | 'mobile' | 'desktop' | 'api' | 'library' | 'cli'
  technologies: string[]
  features: string[]
  complexity: 'simple' | 'medium' | 'complex'
  includeTests: boolean
  includeDocumentation: boolean
}

export interface AIProjectResponse {
  projectName: string
  description: string
  structure: FileNode
  recommendations: string[]
  estimatedTime: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
}

// Mock AI service - in a real app, this would call an actual AI API
export class AIProjectService {
  private static readonly MOCK_DELAY = 2000 // 2 seconds to simulate API call

  static async generateProject(request: AIProjectRequest): Promise<AIProjectResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, this.MOCK_DELAY))

    // Generate project name based on description
    const projectName = this.generateProjectName(request.description)
    
    // Generate project structure based on request
    const structure = this.generateProjectStructure(request)
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(request)
    
    return {
      projectName,
      description: request.description,
      structure,
      recommendations,
      estimatedTime: this.estimateTime(request.complexity),
      difficulty: this.determineDifficulty(request),
    }
  }

  private static generateProjectName(description: string): string {
    // Extract key words and create a project name
    const words = description.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .slice(0, 3)
    
    return words.join('-') || 'ai-generated-project'
  }

  private static generateProjectStructure(request: AIProjectRequest): FileNode {
    const { projectType, technologies, features, includeTests, includeDocumentation } = request

    const createFile = (name: string, content?: string): FileNode => ({
      name,
      type: 'file',
      content: content || `// ${name}\n// Generated by AI\n`,
    })

    const createFolder = (name: string, children: FileNode[] = []): FileNode => ({
      name,
      type: 'directory',
      children,
    })

    // Base structure
    const baseFiles = [
      createFile('.gitignore'),
      createFile('README.md'),
      createFile('package.json'),
    ]

    if (includeDocumentation) {
      baseFiles.push(
        createFolder('docs', [
          createFile('CONTRIBUTING.md'),
          createFile('DEPLOYMENT.md'),
          createFile('API.md'),
        ])
      )
    }

    // Technology-specific structure
    let srcStructure: FileNode[] = []

    if (technologies.includes('React') || technologies.includes('Vue') || technologies.includes('Angular')) {
      srcStructure = [
        createFile('main.tsx'),
        createFile('App.tsx'),
        createFile('index.css'),
        createFolder('components', [
          createFile('Header.tsx'),
          createFile('Footer.tsx'),
          createFile('Button.tsx'),
        ]),
        createFolder('pages', [
          createFile('Home.tsx'),
          createFile('About.tsx'),
        ]),
        createFolder('hooks', [
          createFile('useApi.ts'),
        ]),
        createFolder('utils', [
          createFile('helpers.ts'),
          createFile('constants.ts'),
        ]),
        createFolder('types', [
          createFile('index.ts'),
        ]),
      ]

      if (features.includes('routing')) {
        srcStructure.push(
          createFolder('router', [
            createFile('index.ts'),
          ])
        )
      }

      if (features.includes('state-management')) {
        srcStructure.push(
          createFolder('store', [
            createFile('index.ts'),
            createFile('userStore.ts'),
          ])
        )
      }

      if (features.includes('api-integration')) {
        srcStructure.push(
          createFolder('services', [
            createFile('api.ts'),
            createFile('userService.ts'),
          ])
        )
      }
    }

    if (technologies.includes('Express') || technologies.includes('FastAPI') || technologies.includes('Django')) {
      srcStructure = [
        createFile('app.ts'),
        createFile('server.ts'),
        createFolder('controllers', [
          createFile('userController.ts'),
          createFile('authController.ts'),
        ]),
        createFolder('models', [
          createFile('User.ts'),
        ]),
        createFolder('routes', [
          createFile('users.ts'),
          createFile('auth.ts'),
        ]),
        createFolder('middleware', [
          createFile('auth.ts'),
          createFile('errorHandler.ts'),
        ]),
        createFolder('utils', [
          createFile('database.ts'),
          createFile('logger.ts'),
        ]),
      ]

      if (features.includes('authentication')) {
        srcStructure.push(
          createFolder('auth', [
            createFile('jwt.ts'),
            createFile('passport.ts'),
          ])
        )
      }

      if (features.includes('database')) {
        srcStructure.push(
          createFolder('database', [
            createFile('connection.ts'),
            createFile('migrations.ts'),
          ])
        )
      }
    }

    // Add testing structure if requested
    if (includeTests) {
      baseFiles.push(
        createFolder('tests', [
          createFile('setup.ts'),
          createFolder('unit', [
            createFile('example.test.ts'),
          ]),
          createFolder('integration', [
            createFile('api.test.ts'),
          ]),
        ])
      )
    }

    // Add configuration files based on technologies
    if (technologies.includes('TypeScript')) {
      baseFiles.push(createFile('tsconfig.json'))
    }

    if (technologies.includes('Tailwind')) {
      baseFiles.push(
        createFile('tailwind.config.js'),
        createFile('postcss.config.js')
      )
    }

    if (technologies.includes('Vite')) {
      baseFiles.push(createFile('vite.config.ts'))
    }

    if (technologies.includes('Next.js')) {
      baseFiles.push(createFile('next.config.js'))
    }

    return createFolder(this.generateProjectName(request.description), [
      ...baseFiles,
      createFolder('src', srcStructure),
      createFolder('public', [
        createFile('favicon.ico'),
      ]),
    ])
  }

  private static generateRecommendations(request: AIProjectRequest): string[] {
    const recommendations: string[] = []

    if (!request.includeTests) {
      recommendations.push('Consider adding unit tests for better code quality')
    }

    if (!request.includeDocumentation) {
      recommendations.push('Add comprehensive documentation for better maintainability')
    }

    if (request.projectType === 'web' && !request.technologies.includes('TypeScript')) {
      recommendations.push('Consider using TypeScript for better type safety')
    }

    if (request.features.includes('api-integration') && !request.features.includes('error-handling')) {
      recommendations.push('Implement proper error handling for API calls')
    }

    if (request.complexity === 'complex' && !request.features.includes('state-management')) {
      recommendations.push('Consider using a state management solution for complex applications')
    }

    recommendations.push('Set up CI/CD pipeline for automated testing and deployment')
    recommendations.push('Configure ESLint and Prettier for code consistency')
    recommendations.push('Add environment variable configuration')

    return recommendations
  }

  private static estimateTime(complexity: 'simple' | 'medium' | 'complex'): string {
    switch (complexity) {
      case 'simple':
        return '1-2 weeks'
      case 'medium':
        return '3-6 weeks'
      case 'complex':
        return '2-4 months'
      default:
        return '2-4 weeks'
    }
  }

  private static determineDifficulty(request: AIProjectRequest): 'Beginner' | 'Intermediate' | 'Advanced' {
    const { complexity, technologies, features } = request

    if (complexity === 'simple' && technologies.length <= 2 && features.length <= 3) {
      return 'Beginner'
    }

    if (complexity === 'complex' || technologies.length > 4 || features.length > 6) {
      return 'Advanced'
    }

    return 'Intermediate'
  }
}
